<div class="fixed inset-0 z-[99] flex flex-col overflow-hidden bg-base-100 h-screen w-full"
    x-data="{
        activeTab: 'chat',
        isMobileView: false,
        checkMobileView() {
            this.isMobileView = window.innerWidth <= 768;
            return this.isMobileView;
        }
    }"
    x-init="
        checkMobileView();
        window.addEventListener('resize', () => checkMobileView());
    "
>
    <style>
        /* Save indicator styling */
        .quill-save-indicator {
            position: absolute;
            right: 10px;
            bottom: 10px;
            font-size: 12px;
            color: #666;
            background-color: rgba(255, 255, 255, 0.8);
            padding: 2px 8px;
            border-radius: 4px;
            z-index: 10;
            transition: opacity 0.3s ease;
        }

        @keyframes pulse-subtle {

            0%,
            100% {
                opacity: 1;
            }

            50% {
                opacity: 0.7;
            }
        }

        .thinking-pulse {
            animation: pulse-subtle 2s infinite ease-in-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }

            to {
                opacity: 1;
            }
        }

        @keyframes shine {
            0% {
                background-position: -100% 0;
                opacity: 0.7;
            }
            50% {
                opacity: 1;
            }
            100% {
                background-position: 200% 0;
                opacity: 0.7;
            }
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.2);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            animation: fadeIn 0.3s ease-in-out;
            width: 100vw;
            height: 100vh;
        }

        .loading-content {
            background-color: var(--b1);
            border-radius: 0.5rem;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
            max-width: 90%;
            width: 24rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        @keyframes spin {
            0% {`
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* Ensure loading spinner is centered */
        .loading-content .loading {
            margin: 0 auto;
            display: block;
        }

        /* Define explicit colors for mobile tabs */
        .mobile-tab-colors {
            --tab-active-bg: #e6f0ff; /* Light blue background */
            --tab-active-border: #3b82f6; /* Blue border */
            --tab-active-text: #1d4ed8; /* Darker blue text */
            --tab-active-shadow: rgba(59, 130, 246, 0.3); /* Blue shadow with opacity */
        }

        /* Mobile view styles */
        .mobile-tabs {
            display: none;
        }

        @media (max-width: 768px) {
            .mobile-tabs {
                display: flex;
                position: sticky;
                top: 0;
                z-index: 10;
                background-color: var(--b2);
                border-bottom: 1px solid var(--bc);
                opacity: 0.95;
            }

            .panel-container {
                flex-direction: column;
            }

            .panel-chat, .panel-editor {
                width: 100% !important;
                height: calc(100vh - 180px);
                display: none;
            }

            .panel-chat.active, .panel-editor.active {
                display: flex;
                flex-direction: column;
            }

            .mobile-tab {
                flex: 1;
                text-align: center;
                padding: 0.75rem;
                cursor: pointer;
                border-bottom: 2px solid transparent;
                font-weight: 500;
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;
                border-radius: 0.25rem 0.25rem 0 0; /* Rounded top corners */
                background-color: rgba(0, 0, 0, 0.03); /* Very light gray for inactive tabs */
                color: #666; /* Darker text for inactive tabs */
            }

            .mobile-tab.active {
                border-bottom-color: var(--tab-active-border, #3b82f6);
                color: var(--tab-active-text, #1d4ed8);
                position: relative;
                background-color: var(--tab-active-bg, #e6f0ff);
                font-weight: 600;
                box-shadow: 0 -2px 8px var(--tab-active-shadow, rgba(59, 130, 246, 0.3));
                transform: translateY(-2px); /* Slight lift effect */
            }

            .mobile-tab.active::after {
                content: '';
                position: absolute;
                bottom: 0;
                left: 0;
                width: 100%;
                height: 4px; /* Thicker for better visibility */
                background: linear-gradient(90deg,
                    transparent,
                    var(--tab-active-border, #3b82f6),
                    var(--tab-active-border, #3b82f6),
                    transparent
                );
                background-size: 200% 100%;
                animation: shine 1.5s infinite linear;
                z-index: 1; /* Ensure it's above other elements */
            }
        }
    </style>


    <!-- Editor Header -->
    <div class="flex flex-col md:flex-row items-start md:items-center justify-between p-4 border-b border-base-content/10">
        <!-- Left side with title - always visible -->
        <div class="flex items-center mb-3 md:mb-0">
            <div class="flex items-center justify-center w-10 h-10 mr-3 rounded-full bg-primary/20">
                <span class="text-lg">📝</span>
            </div>
            <div>
                <h2 class="text-lg font-semibold">{{ ucfirst($draft->draft_type) }} {{ __('app.editor') }}</h2>
                <p class="text-sm text-base-content/70 truncate max-w-[200px] sm:max-w-xs ">"{{ $draft->description }}"</p>
            </div>
        </div>

        <!-- Right side with actions -->
        <div class="flex flex-col sm:flex-row w-full md:w-auto items-start sm:items-center gap-2">
            <!-- Last saved info -->
            @if ($lastSaved)
                <span class="text-xs text-base-content/60 hidden md:inline-block">{{ __('app.last_saved_at') }} {{ $lastSaved }}</span>
            @endif

            <!-- Action buttons -->
            <div class="flex flex-wrap gap-2 w-full sm:w-auto">
                <!-- Details button -->
                <a href="{{ route('case-files.drafts.details', [$caseFile, $draft]) }}" class="btn btn-ghost btn-sm">
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 md:mr-1" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span class="hidden md:inline">{{ __('app.draft_details') }}</span>
                </a>

                <!-- Generate document button -->
                <button type="button" class="btn btn-primary btn-sm"
                    onclick="document.getElementById('generate-document-modal-{{ $draft->id }}').classList.add('modal-open')">
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 md:mr-1" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                    </svg>
                    <span class="hidden md:inline">{{ __('app.generate_document') }}</span>
                </button>

                <!-- Back button -->
                <a href="{{ route('case-files.drafts.index', $caseFile) }}" class="btn btn-ghost btn-sm">
                    <span>←</span>
                    <span class="hidden md:inline">{{ __('app.back_to_drafts') }}</span>
                    <span class="md:hidden">Back</span>
                </a>
            </div>
        </div>
    </div>
    <x-exhibits-modal :draft="$draft" :caseFile="$caseFile" />

    <!-- Mobile Tab Navigation -->
    <div class="mobile-tabs mobile-tab-colors">
        <div class="mobile-tab" :class="{ 'active': activeTab === 'chat' }" @click="activeTab = 'chat'">
            <div class="flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                </svg>
                <span>Chat</span>
            </div>
        </div>
        <div id="editor-tab" class="mobile-tab" :class="{ 'active': activeTab === 'editor' }" @click="activeTab = 'editor'">
            <div class="flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <span>Editor</span>
            </div>
        </div>
    </div>

    <!-- Split Panel Layout -->
    <div class="flex flex-1 overflow-hidden panel-container" x-data="{
        notifications: [],
        addNotification(message, type = 'info') {
            const id = Date.now();
            this.notifications.push({ id, message, type });
            setTimeout(() => this.removeNotification(id), 5000);
        },
        removeNotification(id) {
            this.notifications = this.notifications.filter(n => n.id !== id);
        },
        init() {
            // Listen for notify events from Livewire
            Livewire.on('notify', (data) => {
                this.addNotification(data[0].message, data[0].type);
            });

            // Listen for showAiSuggestionsModal events
            Livewire.on('showAiSuggestionsModal', () => {
                document.getElementById('ai-suggestions-modal').classList.add('modal-open');
            });

            // Listen for closeAiSuggestionsModal events
            Livewire.on('closeAiSuggestionsModal', () => {
                document.getElementById('ai-suggestions-modal').classList.remove('modal-open');
            });

            // Debug Livewire events
            document.addEventListener('livewire:initialized', () => {
                console.log('Livewire initialized');
            });

            // Ensure modal buttons work with Livewire
            document.addEventListener('click', (e) => {
                // Check if the click was inside the modal
                if (e.target.closest('#ai-suggestions-modal')) {
                    console.log('Click inside AI suggestions modal', e.target);
                }
            });
        },
        scrollToLastMessage() {
            this.$nextTick(() => {
                // Add a small delay to ensure DOM is fully updated
                setTimeout(() => {
                    const chatContainer = document.getElementById('chat-messages');
                    if (chatContainer) {
                        // Find the last message in the chat
                        const messages = chatContainer.querySelectorAll('.chat');
                        if (messages.length > 0) {
                            const lastMessage = messages[messages.length - 1];
                            // Scroll to the top of the last message
                            lastMessage.scrollIntoView({ behavior: 'smooth', block: 'start' });
                            console.log('Scrolled to last message');
                        } else {
                            // Fallback to scrolling to bottom if no messages found
                            chatContainer.scrollTop = chatContainer.scrollHeight;
                            console.log('No messages found, scrolled to bottom');
                        }
                    }
                }, 100);
            });
        },
        isThinking: false,
        thinkingDots: ''
    }" x-init="scrollToLastMessage();

    // Listen for the scrollChatToBottom event from Livewire
    $wire.on('scrollChatToBottom', () => {
        scrollToLastMessage();
    });

    // Set up a mutation observer to detect when new messages are added
    const chatContainer = document.getElementById('chat-messages');
    if (chatContainer) {
        const observer = new MutationObserver(() => {
            scrollToLastMessage();
        });

        observer.observe(chatContainer, {
            childList: true, // observe direct children
            subtree: true, // and lower descendants too
            characterData: true // observe changes to text content
        });
    }

    // Add Livewire hooks for scrolling after updates
    document.addEventListener('livewire:initialized', () => {
        Livewire.hook('message.processed', (message, component) => {
            if (component.id === $wire.id) {
                scrollToLastMessage();
            }
        });
    });

    // Watch for changes to the isWaitingForResponse property
    $watch('$wire.isWaitingForResponse', (value) => {
        isThinking = value;
        if (value) {
            // Start the thinking animation
            let dots = '';
            const thinkingInterval = setInterval(() => {
                dots = dots.length < 3 ? dots + '.' : '';
                thinkingDots = dots;
            }, 500);

            // Store the interval ID so we can clear it later
            this.thinkingInterval = thinkingInterval;

            // Also scroll to last message when thinking starts
            scrollToLastMessage();
        } else {
            // Clear the thinking animation
            if (this.thinkingInterval) {
                clearInterval(this.thinkingInterval);
            }
            thinkingDots = '';

            // Also scroll to last message when thinking ends
            scrollToLastMessage();
        }
    });"
        @scrollChatToBottom.window="scrollToLastMessage()">
        <!-- Notifications -->
        <div class="fixed z-50 space-y-2 top-4 right-4">
            <template x-for="notification in notifications">
                <div x-show="true" x-transition:enter="transition ease-out duration-300"
                    x-transition:enter-start="opacity-0 transform translate-x-4"
                    x-transition:enter-end="opacity-100 transform translate-x-0"
                    x-transition:leave="transition ease-in duration-200"
                    x-transition:leave-start="opacity-100 transform translate-x-0"
                    x-transition:leave-end="opacity-0 transform translate-x-4"
                    :class="{
                        'alert alert-info': notification.type === 'info',
                        'alert alert-success': notification.type === 'success',
                        'alert alert-warning': notification.type === 'warning',
                        'alert alert-error': notification.type === 'error'
                    }"
                    class="shadow-lg w-72">
                    <div class="flex items-center justify-between w-full">
                        <span x-text="notification.message"></span>
                        <button @click="removeNotification(notification.id)"
                            class="btn btn-ghost btn-xs">&times;</button>
                    </div>
                </div>
            </template>
        </div>

        <!-- Left Panel (AI Chat) -->
        <div class="flex flex-col w-[40%] border-r border-base-content/10 bg-base-200 relative panel-chat" :class="{ 'active': activeTab === 'chat' }">
            <!-- Overlay for waiting state -->
            @if ($isWaitingForResponse)
                <div class="absolute top-0 right-0 z-10 m-4">
                    <div class="gap-1 p-3 shadow-lg badge badge-primary thinking-pulse">
                        <span class="loading loading-spinner loading-xs"></span>
                        <span>{{ __('app.ai_is_thinking') }}<span x-text="thinkingDots"></span></span>
                    </div>
                </div>
            @endif
            <!-- Chat Messages -->
            <div id="chat-messages" class="flex-1 p-4 space-y-4 overflow-y-auto" wire:ignore.self
                x-effect="scrollToLastMessage()">
                <!-- Welcome Message -->
                @if (empty($chatMessages))
                    <div class="mb-4 chat chat-start">
                        <div class="chat-image avatar placeholder">
                            <div class="w-8 rounded-full bg-primary-focus text-primary-content">
                                <span class="text-xs">AI</span>
                            </div>
                        </div>
                        <div class="ml-2">
                            <div class="flex items-center justify-between mb-1">
                                <span class="font-medium">{{ __('app.ai_assistant') }}</span>
                                <time class="ml-2 text-xs opacity-70">{{ now()->format('g:i A') }}</time>
                            </div>
                            <div class="prose-sm prose max-w-none">
                                <p>{{ __('app.ai_welcome_message') }}</p>
                                <ul class="pl-5 mt-2 list-disc">
                                    <li>{{ __('app.generate_content_sections') }}</li>
                                    <li>{{ __('app.create_complete_draft') }}</li>
                                    <li>{{ __('app.answer_legal_questions') }}</li>
                                    <li>{{ __('app.provide_suggestions') }}</li>
                                </ul>
                                <p class="mt-2">{{ __('app.what_work_today') }}</p>
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Chat Messages -->
                @foreach ($chatMessages as $message)
                    <div class="chat {{ $message['role'] === 'user' ? 'chat-end' : 'chat-start' }} mb-4"
                        x-init="scrollToLastMessage()">
{{--                        <div class="chat-image avatar placeholder">--}}
{{--                            <div--}}
{{--                                class="{{ $message['role'] === 'user' ? 'bg-secondary' : 'bg-primary-focus' }} text-{{ $message['role'] === 'user' ? 'secondary' : 'primary' }}-content rounded-full w-8">--}}
{{--                                <span class="text-xs">{{ $message['role'] === 'user' ? __('app.you') : 'AI' }}</span>--}}
{{--                            </div>--}}
{{--                        </div>--}}
                        @if ($message['role'] === 'user')
                            <div class="text-gray-200 shadow-sm chat-bubble bg-primary">
                                <div class="flex items-center justify-between mb-1 chat-header">
                                    <span class="font-medium">You</span>
                                    <time class="ml-2 text-xs opacity-70">{{ $message['timestamp'] }}</time>
                                </div>
                                <div class="prose-sm prose text-gray-200 max-w-none">
                                    <!-- Always display the message content as text -->
                                    {!! nl2br(e($message['content'])) !!}
                                </div>
                            </div>
                        @else
                            <div class="ml-2">
                                <div class="flex items-center justify-between mb-1">
                                    <span class="font-medium">AI Assistant</span>
                                    <time class="ml-2 text-xs opacity-70">{{ $message['timestamp'] }}</time>
                                </div>
                                <div class="prose-sm prose max-w-none">
                                    <!-- Always display the message content as text -->
                                    {!! nl2br(e($message['content'])) !!}
                                </div>
                            </div>
                        @endif
                    </div>
                @endforeach

                <!-- Loading Indicator -->
                @if ($isWaitingForResponse)
                    <div class="chat chat-start">
                        <div class="chat-image avatar placeholder">
                            <div class="w-8 rounded-full bg-primary-focus text-primary-content">
                                <span class="text-xs">AI</span>
                            </div>
                        </div>
                        <div class="ml-2 thinking-pulse">
                            <div class="flex items-center justify-between mb-1">
                                <span class="font-medium">AI Assistant</span>
                                <time class="ml-2 text-xs opacity-70">{{ now()->format('g:i A') }}</time>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="loading loading-dots loading-md"></span>
                                <span>{{ __('app.thinking') }}<span x-text="thinkingDots"></span></span>
                            </div>
                        </div>
                    </div>
                @endif
            </div>

            <!-- Quick Actions -->
            <div class="p-1 border-t border-base-content/10 bg-base-300">
                <div class="flex flex-wrap gap-2">
                    <button class="btn btn-sm btn-primary w-[100%] text-xs" onclick="document.getElementById('generate-document-modal').showModal()" {{ $insufficientCredits ? 'disabled' : '' }}>
                        <i class="fa fa-wand-magic-sparkles"></i> {{ __('app.generate_full_document') }}
                    </button>
                    <button class="btn btn-sm btn-secondary  w-[100%] text-xs" wire:click="generateSectionContent" {{ $insufficientCredits ? 'disabled' : '' }}>
                       <i class="fa fa-wand-magic-sparkles"></i> {{ __('app.generate_current_section') }} - {{ucwords(str_replace('_', ' ', $activeSectionId))}}
                    </button>
                    {{--                    <button class="btn btn-sm btn-accent" wire:click="toggleExhibitSidebar"> --}}
                    {{--                        {{ $showExhibitSidebar ? __('app.hide_exhibits') : __('app.show_exhibits') }} --}}
                    {{--                    </button> --}}

                </div>
            </div>

            <!-- Chat Input -->
            <div class="p-4 border-t border-base-content/10">
                @if($insufficientCredits)
                <div class="alert alert-warning mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" /></svg>
                    <div>
                        <h3 class="font-bold">Insufficient Credits</h3>
                        <div class="text-sm">You need at least {{ $requiredCredits }} credits to use the AI assistant. Your current balance is {{ $currentBalance }} credits.</div>
                        <a href="{{ route('credits.purchase.form') }}" class="btn btn-sm btn-outline mt-2">Purchase Credits</a>
                    </div>
                </div>
                @endif
                <form wire:submit.prevent="sendAiMessage">
                    <div class="flex items-end gap-2">
                        <div class="flex-1">
                            <textarea wire:model="aiMessage" class="w-full textarea textarea-bordered"
                                placeholder="{{ __('app.ask_help_document') }}" rows="3" wire:loading.attr="disabled"
                                wire:target="sendAiMessage" @keydown.enter.prevent="$event.shiftKey || $wire.sendAiMessage()"
                                {{ $insufficientCredits ? 'disabled' : '' }}></textarea>
                        </div>
{{--                        <button type="submit" class="btn btn-primary" wire:loading.attr="disabled"--}}
{{--                            wire:loading.class="opacity-50 cursor-not-allowed" wire:target="sendAiMessage"--}}
{{--                            disabled="{{ $isWaitingForResponse ? 'disabled' : '' }}">--}}
{{--                            @if ($isWaitingForResponse)--}}
{{--                                <span class="mr-2 loading loading-spinner loading-xs"></span>--}}
{{--                                <span>{{ __('app.thinking_ellipsis') }}</span>--}}
{{--                            @else--}}
{{--                                <span wire:loading.remove wire:target="sendAiMessage">--}}
{{--                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 20 20"--}}
{{--                                        fill="currentColor">--}}
{{--                                        <path--}}
{{--                                            d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z" />--}}
{{--                                    </svg>--}}
{{--                                </span>--}}
{{--                                <span wire:loading wire:target="sendAiMessage"--}}
{{--                                    class="loading loading-spinner loading-xs"></span>--}}
{{--                            @endif--}}
{{--                        </button>--}}
                    </div>
                </form>

                <div class="mt-1 text-xs text-base-content/60">
                    {{ __('app.enter_shift_newline') }}
                </div>
            </div>
        </div>

        <!-- Right Panel (Document Editor) -->
        <div class="flex flex-col w-[60%] panel-editor" :class="{ 'active': activeTab === 'editor' }">
            <!-- Exhibit Sidebar (conditionally shown) -->
            {{--            @if ($showExhibitSidebar) --}}
            {{--                <div class="p-4 border-b border-base-content/10 bg-base-200"> --}}
            {{--                    @livewire('drafts.exhibit-sidebar', ['caseFileId' => $caseFile->id, 'draftId' => $draft->id]) --}}
            {{--                </div> --}}
            {{--            @endif --}}
            <!-- Section Navigation -->
            <div class="flex items-center p-2 overflow-x-auto border-b border-base-content/10 bg-base-200" x-data="{
                showSectionMenu: false,
                menuSectionId: null,
                showMenu(sectionId, event) {
                    event.stopPropagation();
                    this.menuSectionId = sectionId;
                    this.showSectionMenu = true;
                },
                hideMenu() {
                    this.showSectionMenu = false;
                    this.menuSectionId = null;
                }
            }" @click.away="hideMenu()">
                @foreach ($sections as $section)
                    <div class="relative mr-1">
                        <!-- Section Button -->
                        <button wire:click="setActiveSection('{{ $section['id'] }}')"
                            class="btn btn-sm {{ $activeSectionId === $section['id'] ? 'btn-primary' : 'btn-ghost' }} group">
                            {{ $section['name'] }}

                            <!-- Section Menu Button (only show on hover or active) -->
                            <button @click.stop="showMenu('{{ $section['id'] }}', $event)"
                                class="ml-1 opacity-0 group-hover:opacity-100 {{ $activeSectionId === $section['id'] ? 'opacity-100' : '' }} transition-opacity">
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>
                        </button>

                        <!-- Section Context Menu -->
                        <div x-show="showSectionMenu && menuSectionId === '{{ $section['id'] }}'"
                            x-transition:enter="transition ease-out duration-100"
                            x-transition:enter-start="transform opacity-0 scale-95"
                            x-transition:enter-end="transform opacity-100 scale-100"
                            x-transition:leave="transition ease-in duration-75"
                            x-transition:leave-start="transform opacity-100 scale-100"
                            x-transition:leave-end="transform opacity-0 scale-95"
                            class="absolute top-full left-0 z-50 mt-1 bg-white border border-gray-200 rounded-md shadow-lg min-w-48"
                            style="display: none;">

                            <div class="py-1">
                                <!-- Add Section After -->
                                <button wire:click="openAddSectionModal('{{ $section['id'] }}')" @click="hideMenu()"
                                    class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                    </svg>
                                    Add Section After
                                </button>

                                @if (!($section['required'] ?? false))
                                    <!-- Delete Section (only for non-required sections) -->
                                    <button wire:click="deleteSection('{{ $section['id'] }}')"
                                        wire:confirm="Are you sure you want to delete the section '{{ $section['name'] }}'? This action cannot be undone."
                                        @click="hideMenu()"
                                        class="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                        </svg>
                                        Delete Section
                                    </button>
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach

                <!-- Add Section Button (at the end) -->
                <button wire:click="openAddSectionModal"
                    class="btn btn-sm btn-outline btn-success ml-2">
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Add Section
                </button>
            </div>

            <!-- Section Editor -->
            <div class="flex-1 p-4 overflow-y-auto">
                @if ($activeSectionId)
                    <div class="mb-4">
                        <div class="flex items-center justify-between mb-2">
                            <h3 class="text-lg font-medium">
                                @foreach ($sections as $section)
                                    @if ($section['id'] === $activeSectionId)
                                        {{ $section['name'] }}
                                    @endif
                                @endforeach
                            </h3>
{{--                            <button wire:click="updateSectionContent" class="btn btn-sm btn-primary">--}}
{{--                                {{ __('app.save_section') }}--}}
{{--                            </button>--}}
                        </div>
                        @php
                            // Check if this is a caption section for a motion or pleading
                            $isCaptionSection = $activeSectionId === 'caption';
                            $isMotionOrPleading = in_array($draft->draft_type, $courtDocTypes);
                            $useCaptionForm = $isCaptionSection && $isMotionOrPleading;
                        @endphp

                        @if ($useCaptionForm)
                            {{-- Caption Form Editor for Motions/Pleadings --}}
                            @livewire(
                                'drafts.caption-form-editor',
                                [
                                    'content' => $activeSectionContent,
                                    'draftId' => $draft->id,
                                    'caseFileId' => $caseFile->id,
                                ],
                                key('caption-form-static')
                            )

                            {{-- Add a hidden debug field to show the current content --}}
                            <div class="p-2 mt-4 text-xs rounded bg-base-200">
                                <strong>{{ __('app.debug') }}</strong> {{ __('app.content_length') }}
                                {{ strlen($activeSectionContent) }} {{ __('app.bytes') }}
                                <button wire:click="updateSectionContent" class="ml-2 btn btn-xs btn-primary">
                                    {{ __('app.save_caption') }}
                                </button>
                            </div>
                        @endif
                        @if (!empty($aiSuggestions))
                            <button class="my-3 bg-green-600 btn btn-sm"
                                onclick="document.getElementById('ai-suggestions-modal').classList.add('modal-open')">
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-1" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M13 10V3L4 14h7v7l9-11h-7z" />
                                </svg>
                                {{ __('app.view_suggestions') }} <span
                                    class="badge badge-sm">{{ count($aiSuggestions) }}</span>
                            </button>
                        @endif


                        {{-- Quill Editor for regular sections --}}
                        <div wire:show="activeSectionId !== 'caption'" wire:ignore x-data="{ componentId: $el.closest('[wire\\:id]')?.getAttribute('wire:id') }"
                            x-init="console.log('Livewire component ID:', componentId)" class="rounded-lg border border-4 border-primary">
                            <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
                            <div id="quill-editor-{{ $activeSectionId }}" style="min-height: 300px;"></div>

                            <script src="https://js.pusher.com/8.2.0/pusher.min.js"></script>
                            <script>
                                // Initialize Pusher
                                const pusher = new Pusher('{{ config('broadcasting.connections.pusher.key') }}', {
                                    cluster: '{{ config('broadcasting.connections.pusher.options.cluster') }}',
                                    encrypted: true
                                });

                                // Subscribe to the private channel for this draft
                                const channel = pusher.subscribe('draft.{{ $draftId }}');

                                // Listen for AI response events
                                channel.bind('ai.response.processed', function(data) {
                                    console.log('Received AI response from Pusher:', data);
                                    // The Livewire component will handle this event

                                    // Manually dispatch the event to Livewire
                                    @this.call('handleAiResponseProcessed', data);
                                });

                                // Listen for section content generation events
                                channel.bind('section.content.generated', function(data) {
                                    console.log('Received section content from Pusher:', data);
                                    // The Livewire component will handle this event
                                    // Manually dispatch the event to Livewire
                                    @this.call('handleSectionContentGenerated', data);
                                });

                                // Listen for console log events from Livewire
                                document.addEventListener('consoleLog', function(e) {
                                    console.log(e.detail.message, e.detail.data);
                                });

                                function removeHTMLFromString(htmlString) {
                                    if (!htmlString || typeof htmlString !== 'string') {
                                        return htmlString;
                                    }

                                    // Check if the string is wrapped in HTML tags
                                    const htmlRegex = /<([a-z]+)[^>]*>(.*)<\/\1>/is;
                                    const match = htmlString.match(htmlRegex);

                                    if (match) {
                                        console.log('Found HTML tags in string, extracting content');
                                        // Extract the content from inside the tags
                                        return match[2];
                                    }

                                    // Create a temporary div element
                                    const tempDiv = document.createElement('div');

                                    // Set the innerHTML of the div to the HTML string
                                    tempDiv.innerHTML = htmlString;

                                    // Get the text content of the div, which automatically strips HTML
                                    return tempDiv.textContent || tempDiv.innerText || "";
                                }

                                document.addEventListener('DOMContentLoaded', function() {
                                    console.log('Initializing Quill editor');
                                    // Initialize Quill editor when the DOM is fully loaded
                                    if (typeof window.initQuillEditor === 'function') {
                                        // Log the content we're initializing with
                                        console.log('Initializing Quill with content:', @js($activeSectionContent));

                                        // Parse the content if it's a JSON string
                                        let initialContent = @js($activeSectionContent);

                                        // Pre-process the content to ensure it's properly parsed
                                        if (typeof initialContent === 'string') {
                                            // First, check if the content is wrapped in HTML tags and extract it
                                            const cleanContent = removeHTMLFromString(initialContent);
                                            // console.log('Cleaned content:', cleanContent);

                                            // Check if it looks like JSON
                                            if (cleanContent.trim().startsWith('{')) {
                                                try {
                                                    // Try to parse as JSON
                                                    const jsonObj = JSON.parse(cleanContent);

                                                    // If it's a valid Delta object (has 'ops' property), use it directly
                                                    if (jsonObj.ops) {
                                                        // console.log('Pre-processed content as Delta object');
                                                        initialContent = jsonObj;
                                                    }
                                                    // If it's an EditorJS object (has 'blocks' property), convert it to Quill Delta
                                                    else if (jsonObj.blocks && Array.isArray(jsonObj.blocks)) {
                                                        console.log('Pre-processed content as EditorJS object');
                                                        if (typeof window.convertEditorJSToQuill === 'function') {
                                                            initialContent = window.convertEditorJSToQuill(jsonObj);
                                                            console.log('Converted EditorJS to Quill Delta:', initialContent);
                                                        } else {
                                                            console.warn('EditorJS converter not available');
                                                            // Extract text from blocks as fallback
                                                            let extractedText = '';
                                                            jsonObj.blocks.forEach(block => {
                                                                if (block.data && block.data.text) {
                                                                    extractedText += block.data.text + '\n\n';
                                                                }
                                                            });
                                                            initialContent = extractedText || initialContent;
                                                        }
                                                    }
                                                } catch (e) {
                                                    console.error('Error pre-processing content:', e);
                                                    // Try to see if there's JSON inside HTML tags
                                                    const jsonRegex = /<p>(\{.*\})<\/p>/s;
                                                    const match = initialContent.match(jsonRegex);

                                                    if (match) {
                                                        console.log('Found JSON inside HTML tags, trying to parse again');
                                                        try {
                                                            const extractedJson = match[1];
                                                            const jsonObj = JSON.parse(extractedJson);

                                                            if (jsonObj.ops) {
                                                                console.log('Successfully parsed JSON from HTML tags');
                                                                initialContent = jsonObj;
                                                            }
                                                        } catch (innerError) {
                                                            console.error('Error parsing JSON from HTML tags:', innerError);
                                                            // Keep the original content if parsing fails
                                                        }
                                                    } else {
                                                        // Keep the original content if parsing fails
                                                    }
                                                }
                                            }
                                        }

                                        const quill = window.initQuillEditor('quill-editor-{{ $activeSectionId }}', initialContent,
                                            function(
                                                content) {
                                                // Update Livewire property when content changes
                                                // We're using the Delta object (JSON) to preserve all formatting
                                                // Use @this.set instead of window.Livewire.find() to avoid component ID issues
                                                // console.log("INCOMING CONTENT:");
                                                // console.log(content)
                                                // console.log(content.delta)
                                                let contentChanged = @this.activeSectionContent !== JSON.stringify(content.delta);
                                                // console.log('contentChanged: ');
                                                // console.log(contentChanged);

                                                if(contentChanged) {
                                                    console.log('Content changed, updating section content');
                                                    @this.set('activeSectionContent', JSON.stringify(content.delta));
                                                    @this.updateSectionContent(true);
                                                }


                                                // Return a promise to indicate when the save is complete
                                                return new Promise((resolve) => {
                                                    // Simulate a short delay to show the saving indicator
                                                    setTimeout(() => {
                                                        resolve();
                                                    }, 300);
                                                });
                                            },
                                            {
                                                // Enable auto-save by default
                                                autoSave: true,
                                                // Set auto-save delay to 1 second
                                                autoSaveDelay: 1000,
                                                // Show save indicator
                                                showSaveIndicator: true
                                            });

                                        // Manually trigger the update-editor-content event to ensure proper rendering
                                        setTimeout(() => {
                                            // Only trigger if we have content
                                            if (initialContent) {
                                                console.log('Manually triggering update-editor-content event');
                                                // Use the same event format that the PHP side uses
                                                if (window.Livewire.emit) {
                                                    // For older Livewire versions
                                                    window.Livewire.emit('update-editor-content', {
                                                        content: typeof initialContent === 'object' ? JSON.stringify(
                                                            initialContent) : initialContent
                                                    });
                                                } else {
                                                    // For newer Livewire versions
                                                    window.Livewire.dispatch('update-editor-content', {
                                                        content: typeof initialContent === 'object' ? JSON.stringify(
                                                            initialContent) : initialContent
                                                    });
                                                }
                                            }
                                        }, 100); // Small delay to ensure Quill is fully initialized

                                        // Listen for the insertTextAtCursor event from Livewire
                                        Livewire.on('insertTextAtCursor', data => {
                                            console.log('insertTextAtCursor event received:', data);
                                            const {
                                                text,
                                                editorId
                                            } = data;
                                            // Use our quillInsertText helper function
                                            if (window.quillInsertHTML && window.quillInsertHTML(
                                                    'quill-editor-{{ $activeSectionId }}', text)) {
                                                window.showNotification('Content inserted successfully', 'success');
                                            } else {
                                                window.showNotification('Failed to insert content', 'error');
                                            }
                                        });

                                        // Listen for section-content-loaded event
                                        Livewire.on('section-content-loaded', data => {
                                            const {
                                                sectionId,
                                                content
                                            } = data;
                                            console.log('Section content loaded event received:', {
                                                sectionId,
                                                contentLength: content ? content.length : 0
                                            });

                                            // Only update if this is for our section
                                            if (sectionId === '{{ $activeSectionId }}') {
                                                console.log('Updating content for section:', sectionId);
                                                // Update the editor content
                                                if (window.quillInstances && window.quillInstances[
                                                        'quill-editor-{{ $activeSectionId }}']) {
                                                    try {
                                                        // Try to parse as Delta if it's in Delta format
                                                        if (typeof content === 'string') {
                                                            // First, check if the content is wrapped in HTML tags and extract it
                                                            const cleanContent = removeHTMLFromString(content);
                                                            console.log('Cleaned content for section-content-loaded:',
                                                                cleanContent);

                                                            // Check if it looks like JSON
                                                            if (cleanContent.trim().startsWith('{')) {
                                                                try {
                                                                    // Parse the JSON string
                                                                    const jsonObj = JSON.parse(cleanContent);

                                                                    // Check if it's a Delta object (has 'ops' property)
                                                                    if (jsonObj.ops) {
                                                                        console.log('Setting Quill contents with Delta object');
                                                                        window.quillInstances[
                                                                                'quill-editor-{{ $activeSectionId }}']
                                                                            .focus();
                                                                        window.quillInstances[
                                                                                'quill-editor-{{ $activeSectionId }}']
                                                                            .setContents(jsonObj);
                                                                    }
                                                                    // Check if it's an EditorJS object (has 'blocks' property)
                                                                    else if (jsonObj.blocks && Array.isArray(jsonObj.blocks)) {
                                                                        console.log(
                                                                            'Detected EditorJS format, converting to Quill Delta'
                                                                        );
                                                                        // Use the converter function if available
                                                                        if (typeof window.convertEditorJSToQuill === 'function') {
                                                                            const delta = window.convertEditorJSToQuill(jsonObj);
                                                                            console.log('Converted EditorJS to Quill Delta:',
                                                                                delta);
                                                                            window.quillInstances[
                                                                                'quill-editor-{{ $activeSectionId }}'].focus();
                                                                            window.quillInstances[
                                                                                    'quill-editor-{{ $activeSectionId }}']
                                                                                .setContents(
                                                                                    delta);
                                                                        } else {
                                                                            console.warn(
                                                                                'EditorJS converter not available, extracting text'
                                                                            );
                                                                            // Extract text from blocks as fallback
                                                                            let extractedText = '';
                                                                            jsonObj.blocks.forEach(block => {
                                                                                if (block.data && block.data.text) {
                                                                                    extractedText += block.data.text +
                                                                                        '\n\n';
                                                                                }
                                                                            });
                                                                            window.quillInstances[
                                                                                'quill-editor-{{ $activeSectionId }}'].focus();
                                                                            window.quillInstances[
                                                                                'quill-editor-{{ $activeSectionId }}'].setText(
                                                                                extractedText || "{}");
                                                                        }
                                                                    } else {
                                                                        // It's JSON but not a recognized format
                                                                        console.log(
                                                                            'JSON is not a recognized format, using as text');
                                                                        window.quillInstances[
                                                                                'quill-editor-{{ $activeSectionId }}']
                                                                            .focus();
                                                                        window.quillInstances[
                                                                                'quill-editor-{{ $activeSectionId }}']
                                                                            .setText("{}");
                                                                    }
                                                                } catch (e) {
                                                                    console.error('Error parsing JSON:', e);
                                                                    // Try to see if there's JSON inside HTML tags
                                                                    const jsonRegex = /<p>(\{.*\})<\/p>/s;
                                                                    const match = content.match(jsonRegex);

                                                                    if (match) {
                                                                        console.log(
                                                                            'Found JSON inside HTML tags, trying to parse again'
                                                                        );
                                                                        try {
                                                                            const extractedJson = match[1];
                                                                            const jsonObj = JSON.parse(extractedJson);

                                                                            if (jsonObj.ops) {
                                                                                console.log(
                                                                                    'Successfully parsed JSON from HTML tags');
                                                                                window.quillInstances[
                                                                                        'quill-editor-{{ $activeSectionId }}']
                                                                                    .focus();
                                                                                window.quillInstances[
                                                                                        'quill-editor-{{ $activeSectionId }}']
                                                                                    .setContents(jsonObj);
                                                                            }
                                                                        } catch (innerError) {
                                                                            console.error('Error parsing JSON from HTML tags:',
                                                                                innerError);
                                                                            // If parsing fails, set as HTML
                                                                            window.quillInstances[
                                                                                    'quill-editor-{{ $activeSectionId }}']
                                                                                .focus();
                                                                            window.quillInstances[
                                                                                    'quill-editor-{{ $activeSectionId }}'].root
                                                                                .innerHTML = "";
                                                                        }
                                                                    } else {
                                                                        // If parsing fails, set as HTML
                                                                        window.quillInstances[
                                                                                'quill-editor-{{ $activeSectionId }}']
                                                                            .focus();
                                                                        window.quillInstances[
                                                                                'quill-editor-{{ $activeSectionId }}'].root
                                                                            .innerHTML = "";
                                                                    }
                                                                }
                                                            } else {
                                                                // Set as HTML
                                                                console.log('Setting content as HTML');
                                                                window.quillInstances['quill-editor-{{ $activeSectionId }}']
                                                                    .focus();
                                                                window.quillInstances['quill-editor-{{ $activeSectionId }}'].root
                                                                    .innerHTML = "";
                                                            }
                                                        } else {
                                                            // Not a string, can't process
                                                            console.log('Content is not a string, cannot process');
                                                            window.quillInstances['quill-editor-{{ $activeSectionId }}'].focus();
                                                            window.quillInstances['quill-editor-{{ $activeSectionId }}'].root
                                                                .innerHTML = "";
                                                        }
                                                    } catch (error) {
                                                        console.error('Error updating Quill content:', error);
                                                    }
                                                }
                                            }
                                        });

                                        // Listen for update-editor-content event using Livewire's on method
                                        Livewire.on('update-editor-content', data => {
                                            console.log('update-editor-content event received:', data);
                                            const {
                                                content,
                                                editorId
                                            } = data;


                                            if (window.quillInstances && window.quillInstances[
                                                    'quill-editor-{{ $activeSectionId }}']) {
                                                try {
                                                    // Try to parse as Delta if it's in Delta format
                                                    if (typeof content === 'string') {
                                                        // First, check if the content is wrapped in HTML tags and extract it
                                                        const cleanContent = removeHTMLFromString(content);
                                                        console.log('Cleaned content for update-editor-content:', cleanContent);

                                                        // Check if it looks like JSON
                                                        if (cleanContent.trim().startsWith('{')) {
                                                            try {
                                                                // First try to parse the string as JSON
                                                                const jsonObj = JSON.parse(cleanContent);

                                                                // Check if it's a Delta object (has 'ops' property)
                                                                if (jsonObj.ops) {
                                                                    console.log('Setting Quill contents with Delta object');
                                                                    window.quillInstances['quill-editor-{{ $activeSectionId }}']
                                                                        .focus();
                                                                    window.quillInstances['quill-editor-{{ $activeSectionId }}']
                                                                        .setContents(jsonObj);
                                                                }
                                                                // Check if it's an EditorJS object (has 'blocks' property)
                                                                else if (jsonObj.blocks && Array.isArray(jsonObj.blocks)) {
                                                                    console.log(
                                                                        'Detected EditorJS format, converting to Quill Delta');
                                                                    // Use the converter function if available
                                                                    if (typeof window.convertEditorJSToQuill === 'function') {
                                                                        const delta = window.convertEditorJSToQuill(jsonObj);
                                                                        console.log('Converted EditorJS to Quill Delta:', delta);
                                                                        window.quillInstances[
                                                                                'quill-editor-{{ $activeSectionId }}']
                                                                            .focus();
                                                                        window.quillInstances[
                                                                                'quill-editor-{{ $activeSectionId }}']
                                                                            .setContents(delta);
                                                                    } else {
                                                                        console.warn(
                                                                            'EditorJS converter not available, extracting text');
                                                                        // Extract text from blocks as fallback
                                                                        let extractedText = '';
                                                                        jsonObj.blocks.forEach(block => {
                                                                            if (block.data && block.data.text) {
                                                                                extractedText += block.data.text + '\n\n';
                                                                            }
                                                                        });
                                                                        window.quillInstances[
                                                                                'quill-editor-{{ $activeSectionId }}']
                                                                            .focus();
                                                                        window.quillInstances[
                                                                                'quill-editor-{{ $activeSectionId }}']
                                                                            .setText(extractedText || "{}");
                                                                    }
                                                                } else {
                                                                    // It's JSON but not a recognized format
                                                                    console.log('JSON is not a recognized format, using as text');
                                                                    window.quillInstances['quill-editor-{{ $activeSectionId }}']
                                                                        .focus();
                                                                    window.quillInstances['quill-editor-{{ $activeSectionId }}']
                                                                        .setText("{}");
                                                                }
                                                            } catch (e) {
                                                                console.error('Error parsing JSON:', e);
                                                                // Try to see if there's JSON inside HTML tags
                                                                const jsonRegex = /<p>(\{.*\})<\/p>/s;
                                                                const match = content.match(jsonRegex);

                                                                if (match) {
                                                                    console.log(
                                                                        'Found JSON inside HTML tags, trying to parse again');
                                                                    try {
                                                                        const extractedJson = match[1];
                                                                        const jsonObj = JSON.parse(extractedJson);

                                                                        if (jsonObj.ops) {
                                                                            console.log('Successfully parsed JSON from HTML tags');
                                                                            window.quillInstances[
                                                                                    'quill-editor-{{ $activeSectionId }}']
                                                                                .focus();
                                                                            window.quillInstances[
                                                                                    'quill-editor-{{ $activeSectionId }}']
                                                                                .setContents(jsonObj);
                                                                        }
                                                                    } catch (innerError) {
                                                                        console.error('Error parsing JSON from HTML tags:',
                                                                            innerError);
                                                                        // If parsing fails, set as HTML
                                                                        window.quillInstances[
                                                                                'quill-editor-{{ $activeSectionId }}'].root
                                                                            .innerHTML = content;
                                                                    }
                                                                } else {
                                                                    // If parsing fails, set as HTML
                                                                    window.quillInstances['quill-editor-{{ $activeSectionId }}']
                                                                        .root
                                                                        .innerHTML = content;
                                                                }
                                                            }
                                                        } else {
                                                            // Set as HTML
                                                            console.log('Setting content as HTML');
                                                            window.quillInstances['quill-editor-{{ $activeSectionId }}'].root
                                                                .innerHTML = content;
                                                        }
                                                    } else {
                                                        // Not a string, can't process
                                                        console.log('Content is not a string, cannot process');
                                                        window.quillInstances['quill-editor-{{ $activeSectionId }}'].focus();
                                                        window.quillInstances['quill-editor-{{ $activeSectionId }}'].root
                                                            .innerHTML = "";
                                                    }
                                                    console.log('Updated Quill editor content from update-editor-content event');
                                                } catch (error) {
                                                    console.error('Error updating Quill content:', error);
                                                }
                                            }
                                        });
                                    } else {
                                        console.error('Quill editor initialization function not found');
                                    }
                                });
                            </script>
                        </div>


                        <!-- AI Suggestions -->

                        <!-- JavaScript for handling text insertion into the TipTap editor -->

                    </div>
                @else
                    <div class="flex items-center justify-center h-full">
                        <div class="text-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="w-12 h-12 mx-auto text-base-content/30"
                                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            <p class="mt-4 text-lg font-medium">{{ __('app.select_section_edit') }}</p>
                            <p class="mt-2 text-sm text-base-content/70">{{ __('app.click_section_start') }}
                            </p>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- AI Suggestions Modal -->
    <div id="ai-suggestions-modal" class="modal" x-data>
        <div class="w-11/12 max-w-5xl modal-box">
            <h3 class="mb-4 text-lg font-bold">{{ __('app.ai_suggestions') }}</h3>

            @if (!empty($aiSuggestions))
                <div class="space-y-4 max-h-[60vh] overflow-y-auto p-2">
                    @foreach ($aiSuggestions as $index => $suggestion)
                        <div class="p-4 border rounded-lg bg-base-200">
                            <div class="flex items-start justify-between mb-2">
                                <span class="text-sm text-base-content/70">{{ $suggestion['timestamp'] }}</span>
                                <div class="flex space-x-2">
                                    @if (isset($suggestion['type']) && $suggestion['type'] === 'section_content')
                                        <button wire:click="applySectionSuggestion({{ $index }})"
                                            class="btn btn-sm btn-primary">
                                            {{ __('app.apply_to_section') }}
                                        </button>
                                    @else
                                        <button wire:click="insertContentDirectly('{{ $index }}')"
                                            class="btn btn-sm btn-primary">
                                            {{ __('app.insert') }}
                                        </button>
                                    @endif
                                    <button wire:click="removeSuggestion({{ $index }})"
                                        class="btn btn-sm btn-ghost">
                                        {{ __('app.dismiss') }}
                                    </button>
                                </div>
                            </div>
                            <div
                                class="p-3 overflow-y-auto whitespace-pre-wrap border rounded bg-base-100 border-base-300 max-h-60">
                                {{ $this->parseQuillDeltaToText($suggestion['content']) }}
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="p-6 text-center">
                    <p class="text-base-content/70">{{ __('app.no_suggestions_available') }}</p>
                </div>
            @endif

            <div class="modal-action">
                <button class="btn"
                    onclick="document.getElementById('ai-suggestions-modal').classList.remove('modal-open')">{{ __('common.close') }}</button>
            </div>
        </div>
        <div class="modal-backdrop"
            onclick="document.getElementById('ai-suggestions-modal').classList.remove('modal-open')"></div>
    </div>
    <!-- Loading Overlay -->
    <div class="loading-overlay" wire:loading.delay wire:target="sendAiMessage,updateSectionContent,setActiveSection"
        x-data="{
            showOverlay: true,
            init() {
                this.$watch('$wire.isWaitingForResponse', (value) => {
                    if (value) this.showOverlay = true;
                });
            }
        }" x-show="showOverlay">
        <div class="loading-content">
            <div class="mx-auto mb-4 text-white loading loading-lg"></div>
            {{--            <p class="p-2 text-black bg-white rounded-lg">{{ __('app.processing_your_request') }}</p> --}}

            <button @click="showOverlay = false"
                class="flex items-center justify-center w-8 h-8 text-black transition-colors bg-white rounded-full hover:bg-white/30">
                <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd"
                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                        clip-rule="evenodd" />
                </svg>
            </button>
        </div>
    </div>

<!-- Add Section Modal -->
@if ($showAddSectionModal)
    <div class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div class="p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Add New Section</h3>

                <form wire:submit.prevent="createSection">
                    <div class="mb-4">
                        <label for="newSectionName" class="block text-sm font-medium text-gray-700 mb-2">
                            Section Name
                        </label>
                        <input type="text"
                            id="newSectionName"
                            wire:model.live="newSectionName"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="Enter section name..."
                            autofocus>
                        @error('newSectionName')
                            <span class="text-red-500 text-sm">{{ $message }}</span>
                        @enderror

                        @if (!empty($newSectionName))
                            <div class="mt-2 p-2 bg-gray-50 rounded-md">
                                <p class="text-xs text-gray-600">
                                    <span class="font-medium">Section ID:</span>
                                    <code class="bg-gray-200 px-1 rounded">{{ $this->previewSectionId($newSectionName) }}</code>
                                </p>
                            </div>
                        @endif
                    </div>

                    @if ($addAfterSectionId)
                        <div class="mb-4 p-3 bg-blue-50 rounded-md">
                            <p class="text-sm text-blue-700">
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                This section will be added after "{{ collect($sections)->firstWhere('id', $addAfterSectionId)['name'] ?? 'Unknown' }}"
                            </p>
                        </div>
                    @else
                        <div class="mb-4 p-3 bg-green-50 rounded-md">
                            <p class="text-sm text-green-700">
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                This section will be added at the end of the document
                            </p>
                        </div>
                    @endif

                    <div class="flex justify-end space-x-3">
                        <button type="button"
                            wire:click="closeAddSectionModal"
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500">
                            Cancel
                        </button>
                        <button type="submit"
                            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            Add Section
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endif

<!-- Generate Document Confirmation Modal -->
<dialog id="generate-document-modal" class="modal">
    <div class="modal-box">
        <h3 class="font-bold text-lg">Generate Full Document</h3>
        <p class="py-4">This process will generate content for all sections of your document. It may take several minutes to complete.</p>
        <p class="py-2 text-warning"><i class="fa fa-exclamation-triangle"></i> Please do not leave this page until you receive a notification that the document generation is complete.</p>
        <div class="modal-action">
            <form method="dialog">
                <button class="btn btn-ghost">Cancel</button>
            </form>
            <button class="btn btn-primary" wire:click="generateEntireDocument" onclick="document.getElementById('generate-document-modal').close()">Proceed with Generation</button>
        </div>
    </div>
</dialog>
</div>

<script>
    // Simple script to handle tab switching when new suggestions arrive
    document.addEventListener('DOMContentLoaded', function() {
        // Listen for the Livewire event
        if (window.Livewire) {
            window.Livewire.on('switchToEditorTab', function() {
                console.log('Received switchToEditorTab event');
                // Simply click the editor tab
                const editorTab = document.getElementById('editor-tab');
                if (editorTab) {
                    editorTab.click();
                    console.log('Clicked editor tab');
                }
            });
        }
    });
</script>
